import { httpBase } from '../http/httpBase'

/**
 * Types for Editor Service API
 */
export interface GeneratedPrompt {
  _id: string
  content: string
  user_id: string
  task_set_id?: string
  created_at: string
  status: 'pending' | 'success' | 'failed'
}

export interface GenerateContentRequest {
  content: string
}

export interface GenerateContentResponse {
  content: string
  generated_by: string
  status: string
  message: string
}

export interface GetPromptsResponse {
  data: GeneratedPrompt[]
}

/**
 * Editor Service
 * Handles content generation and prompt management using v2 API endpoints
 */
export class EditorService {
  private static readonly BASE_PATH = '/v2/editor'

  /**
   * Generate new content from prompt using v2/editor/generate endpoint
   */
  static async generateContent(content: string): Promise<GenerateContentResponse> {
    const response = await httpBase.post(`${this.BASE_PATH}/generate`, { content })
    return response.data
  }

  /**
   * Get generated prompts history using v2/editor/get_prompts endpoint
   */
  static async getPrompts(): Promise<GetPromptsResponse> {
    const response = await httpBase.post(`${this.BASE_PATH}/get_prompts`)
    return response.data
  }
}

/**
 * Export individual methods for easier importing
 */
export const {
  generateContent,
  getPrompts
} = EditorService
