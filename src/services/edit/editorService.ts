import axios, { AxiosInstance } from 'axios'

/**
 * Types for Editor Service API
 */
export interface GeneratedPrompt {
  _id: string
  content: string
  user_id: string
  task_set_id?: string
  created_at: string
  status: 'pending' | 'success' | 'failed'
}

export interface GenerateContentRequest {
  content: string
}

export interface GenerateContentResponse {
  task_id: string
  status: string
  message: string
}

export interface GetPromptsResponse {
  data: GeneratedPrompt[]
  meta: {
    page: number
    limit: number
    total: number
    total_pages: number
    sort_order: string
    user_id: string
  }
}

/**
 * Editor Service
 * Handles content generation and prompt management using v2 API endpoints
 */
export class EditorService {
  private static readonly BASE_PATH = '/editor'
  private static httpClient: AxiosInstance

  /**
   * Get or create the HTTP client with v2 base URL
   */
  private static getHttpClient(): AxiosInstance {
    if (!this.httpClient) {
      // Get base URL and replace /v1 with /v2
      const baseURL = import.meta.env.VITE_API_URL || 'http://localhost:8204/v1'
      const v2BaseURL = baseURL.replace(/\/v1$/, '/v2')

      this.httpClient = axios.create({
        baseURL: v2BaseURL,
        timeout: 30000,
        headers: {
          'Content-Type': 'application/json',
        },
      })

      // Add request interceptor for auth token
      this.httpClient.interceptors.request.use(
        (config) => {
          const token = localStorage.getItem('auth_token')
          if (token) {
            config.headers.Authorization = `Bearer ${token}`
          }
          console.log(`🚀 Editor Service ${config.method?.toUpperCase()} ${config.url}`)
          return config
        },
        (error) => {
          console.error('❌ Editor Service Request Error:', error)
          return Promise.reject(error)
        }
      )
    }
    return this.httpClient
  }

  /**
   * Generate new content from prompt using v2/editor/generate endpoint
   */
  static async generateContent(content: string): Promise<GenerateContentResponse> {
    const client = this.getHttpClient()
    const response = await client.post(`${this.BASE_PATH}/generate`, { content })
    return response.data
  }

  /**
   * Get generated prompts history using v1/management/editor/get_prompts endpoint
   */
  static async getPrompts(page: number = 1, limit: number = 10, sortOrder: 'asc' | 'desc' = 'desc'): Promise<GetPromptsResponse> {
    // Use v1 base URL for get_prompts
    const baseURL = import.meta.env.VITE_API_URL || 'http://localhost:8204/v1'
    const v1Client = axios.create({
      baseURL: baseURL,
      timeout: 30000,
      headers: {
        'Content-Type': 'application/json',
      },
    })

    // Add auth token
    const token = localStorage.getItem('auth_token')
    if (token) {
      v1Client.defaults.headers.Authorization = `Bearer ${token}`
    }

    const response = await v1Client.post(`/management/editor/get_prompts?page=${page}&limit=${limit}&sort_order=${sortOrder}`)
    return response.data
  }
}

/**
 * Export individual methods for easier importing
 */
export const {
  generateContent,
  getPrompts
} = EditorService
