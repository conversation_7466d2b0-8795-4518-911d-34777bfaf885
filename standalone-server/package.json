{"name": "audio-quiz-genie-standalone-server", "version": "1.0.0", "description": "Standalone server for content generation and prompts management", "main": "dist/index.js", "type": "module", "scripts": {"dev": "tsx watch src/index.ts", "build": "tsc", "start": "node dist/index.js", "lint": "eslint src/**/*.ts", "test": "jest"}, "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "morgan": "^1.10.0", "dotenv": "^16.3.1", "axios": "^1.6.0", "zod": "^3.22.4", "jsonwebtoken": "^9.0.2", "compression": "^1.7.4"}, "devDependencies": {"@types/express": "^4.17.21", "@types/cors": "^2.8.17", "@types/morgan": "^1.9.9", "@types/node": "^20.10.0", "@types/jsonwebtoken": "^9.0.5", "@types/compression": "^1.7.5", "typescript": "^5.3.0", "tsx": "^4.6.0", "eslint": "^8.56.0", "@typescript-eslint/eslint-plugin": "^6.15.0", "@typescript-eslint/parser": "^6.15.0", "jest": "^29.7.0", "@types/jest": "^29.5.8"}, "keywords": ["express", "typescript", "content-generation", "api"], "author": "Audio Quiz Genie Team", "license": "MIT"}